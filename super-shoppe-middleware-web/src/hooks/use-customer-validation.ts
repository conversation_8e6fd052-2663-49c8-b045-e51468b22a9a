import { useCustomer } from '@contexts/customer.context';
import { useUI } from '@contexts/ui.context';
import { removeToken } from '@framework/utils/get-token';
import { STORAGE_KEY_ENUM } from '@utils/use-local-storage';
import { toast } from 'react-toastify';

/**
 * Hook to validate customer account status and handle violations
 */
export const useCustomerValidation = () => {
  const { customer } = useCustomer();
  const { unauthorize } = useUI();

  /**
   * Check if customer account is valid and active
   * @returns boolean - true if valid, false if invalid
   */
  const validateCustomerAccount = (): boolean => {
    if (!customer) {
      return false;
    }

    // Check if customer is active
    if (!customer.is_active) {
      handleAccountViolation('Your account has been deactivated. Please contact support.');
      return false;
    }

    // Check if customer is blocked
    if (customer.is_book_blocked) {
      handleAccountViolation('Your account has been temporarily blocked.');
      return false;
    }

    return true;
  };

  /**
   * Handle account violations by clearing auth and showing message
   */
  const handleAccountViolation = (message: string) => {
    removeToken(STORAGE_KEY_ENUM.AUTH_TOKEN);
    unauthorize();
    toast.error(message);
  };

  /**
   * Check if email verification is required
   */
  const isEmailVerificationRequired = (): boolean => {
    return !!(customer?.email && !customer.email_verified_at);
  };

  /**
   * Check if phone verification is required
   */
  const isPhoneVerificationRequired = (): boolean => {
    return !!(customer?.phone && !customer.phone_verified_at);
  };

  /**
   * Get customer account status summary
   */
  const getAccountStatus = () => {
    if (!customer) {
      return {
        isValid: false,
        isActive: false,
        isBlocked: false,
        emailVerified: false,
        phoneVerified: false,
      };
    }

    return {
      isValid: validateCustomerAccount(),
      isActive: customer.is_active,
      isBlocked: customer.is_book_blocked || false,
      emailVerified: !!customer.email_verified_at,
      phoneVerified: !!customer.phone_verified_at,
    };
  };

  return {
    validateCustomerAccount,
    handleAccountViolation,
    isEmailVerificationRequired,
    isPhoneVerificationRequired,
    getAccountStatus,
    customer,
  };
};

export default useCustomerValidation;

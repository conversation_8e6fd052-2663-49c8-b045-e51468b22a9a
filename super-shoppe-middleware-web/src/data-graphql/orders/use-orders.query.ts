import { useQuery, UseQueryOptions } from 'react-query';

import { GET_ORDERS, GET_ORDER, GET_ORDER_STATUSES } from './orders.query';
import {
  OrdersResponse,
  OrderResponse,
  OrderStatusesResponse,
  OrdersQueryVariables,
  Order,
  OrderStatus,
} from './orders.types';
import graphql from '@framework/gql/utils/graphql';

// Fetch orders with pagination and filtering
export const fetchOrders = async (
  variables: OrdersQueryVariables = {}
): Promise<OrdersResponse> => {
  const defaultVariables = {
    first: 15,
    page: 1,
    orderBy: [{ column: 'created_at', order: 'DESC' as const }],
    ...variables,
  };

  return graphql.post(GET_ORDERS, defaultVariables);
};

// Fetch single order by ID
export const fetchOrder = async (id: string): Promise<OrderResponse> => {
  return graphql.post(GET_ORDER, { id });
};

// Fetch order statuses
export const fetchOrderStatuses = async (): Promise<OrderStatusesResponse> => {
  return graphql.post(GET_ORDER_STATUSES);
};

// React Query hook for orders list
export const useOrdersQuery = (
  variables: OrdersQueryVariables = {},
  options?: UseQueryOptions<OrdersResponse, Error>
) => {
  return useQuery<OrdersResponse, Error>(
    ['orders', variables],
    () => fetchOrders(variables),
    {
      keepPreviousData: true,
      staleTime: 30 * 1000, // 30 seconds
      ...options,
    }
  );
};

// React Query hook for single order
export const useOrderQuery = (
  id: string,
  options?: UseQueryOptions<OrderResponse, Error>
) => {
  return useQuery<OrderResponse, Error>(['order', id], () => fetchOrder(id), {
    enabled: !!id,
    staleTime: 60 * 1000, // 1 minute
    ...options,
  });
};

// React Query hook for order statuses
export const useOrderStatusesQuery = (
  options?: UseQueryOptions<OrderStatusesResponse, Error>
) => {
  return useQuery<OrderStatusesResponse, Error>(
    ['orderStatuses'],
    fetchOrderStatuses,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
};

// Helper hook for orders with specific status
export const useOrdersByStatusQuery = (
  status: string,
  variables: Omit<OrdersQueryVariables, 'status'> = {},
  options?: UseQueryOptions<OrdersResponse, Error>
) => {
  return useOrdersQuery({ ...variables, status }, options);
};

// Helper hook for customer orders
export const useCustomerOrdersQuery = (
  customerId: string,
  variables: Omit<OrdersQueryVariables, 'customer_id'> = {},
  options?: UseQueryOptions<OrdersResponse, Error>
) => {
  return useOrdersQuery(
    { ...variables, customer_id: customerId },
    {
      enabled: !!customerId,
      ...options,
    }
  );
};

// Helper function to get status color
export const getOrderStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'pending':
    case 'pending_payment':
      return 'bg-yellow-100 text-yellow-800';
    case 'processing':
    case 'packed':
      return 'bg-blue-100 text-blue-800';
    case 'delivery':
    case 'out_for_delivery':
      return 'bg-purple-100 text-purple-800';
    case 'complete':
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'canceled':
    case 'cancelled':
    case 'failed':
      return 'bg-red-100 text-red-800';
    case 'holded':
    case 'payment_review':
      return 'bg-orange-100 text-orange-800';
    case 'refunded':
    case 'in_refund_process_b':
    case 'in_refund_process_sc':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Helper function to format order status display name
export const formatOrderStatus = (status: string): string => {
  return status
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useCustomer } from '@contexts/customer.context';
import { getToken } from '@framework/utils/get-token';
import { ROUTES } from '@utils/routes';
import BottomNavigation from './bottom-navigation';

interface GlobalLayoutProps {
  children: React.ReactNode;
}

const GlobalLayout: React.FC<GlobalLayoutProps> = ({ children }) => {
  const router = useRouter();
  const { customer, customerLoading } = useCustomer();

  // List of public routes that don't require authentication
  const publicRoutes = [ROUTES.LOGIN, ROUTES.SIGN_UP];
  const isPublicRoute = publicRoutes.includes(router.pathname);

  useEffect(() => {
    const checkAuth = async () => {
      const token = await getToken();

      // If has token but on login page, redirect to home immediately
      if (token && router.pathname === ROUTES.LOGIN) {
        router.replace(ROUTES.HOME);
        return;
      }

      // If no token and trying to access protected route, redirect to login
      if (!token && !isPublicRoute) {
        router.replace(ROUTES.LOGIN);
        return;
      }
    };

    checkAuth();
  }, [router.pathname, isPublicRoute, router]);

  useEffect(() => {
    // If finished loading and no customer data but not on public route, redirect to login
    if (!customerLoading && !customer && !isPublicRoute) {
      router.replace(ROUTES.LOGIN);
    }
  }, [customer, customerLoading, isPublicRoute, router]);

  // Show loading state while checking authentication
  if (customerLoading && !isPublicRoute) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // For public routes, don't show header
  if (isPublicRoute) {
    return <>{children}</>;
  }

  // For protected routes, show content with bottom navigation
  return (
    <div className="min-h-screen bg-gray-100">
      <main className="pb-24">{children}</main>
      <BottomNavigation />
    </div>
  );
};

export default GlobalLayout;

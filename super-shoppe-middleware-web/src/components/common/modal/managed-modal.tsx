import Modal from '@components/common/modal/modal';
import dynamic from 'next/dynamic';
import {
  useModalAction,
  useModalState,
} from '@components/common/modal/modal.context';
const ForgetPassword = dynamic(
  () => import('@components/auth/forget-password/forget-password')
);

const ManagedModal: React.FC = () => {
  const { isOpen, view, closeOnOutsideClick } = useModalState();
  const { closeModal } = useModalAction();

  return (
    <Modal open={isOpen} onClose={closeOnOutsideClick ? closeModal : () => {}}>
      {view === 'FORGET_PASSWORD' && <ForgetPassword />}
    </Modal>
  );
};

export default ManagedModal;

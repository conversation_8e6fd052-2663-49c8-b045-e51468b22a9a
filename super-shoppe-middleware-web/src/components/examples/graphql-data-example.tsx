import React, { useState, useEffect } from 'react';
import { 
  fetchCustomerData, 
  fetchOrdersData, 
  fetchOrderData,
  fetchOrderStatusesData 
} from '@utils/graphql-queries';

/**
 * Example component demonstrating how to use GraphQL queries
 * with graphql.post for fetching customer and order data
 */
const GraphQLDataExample: React.FC = () => {
  const [customerData, setCustomerData] = useState<any>(null);
  const [ordersData, setOrdersData] = useState<any>(null);
  const [orderData, setOrderData] = useState<any>(null);
  const [orderStatuses, setOrderStatuses] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<string>('');

  // Fetch customer data
  const handleFetchCustomer = async () => {
    setLoading(true);
    try {
      const data = await fetchCustomerData();
      setCustomerData(data);
      console.log('Customer Data:', data);
    } catch (error) {
      console.error('Error fetching customer:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch orders data
  const handleFetchOrders = async () => {
    setLoading(true);
    try {
      const data = await fetchOrdersData({
        first: 10,
        page: 1,
        orderBy: [{ column: 'created_at', order: 'DESC' }]
      });
      setOrdersData(data);
      console.log('Orders Data:', data);
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch single order data
  const handleFetchOrder = async () => {
    if (!selectedOrderId) {
      alert('Please enter an order ID');
      return;
    }
    
    setLoading(true);
    try {
      const data = await fetchOrderData(selectedOrderId);
      setOrderData(data);
      console.log('Order Data:', data);
    } catch (error) {
      console.error('Error fetching order:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch order statuses
  const handleFetchOrderStatuses = async () => {
    setLoading(true);
    try {
      const data = await fetchOrderStatusesData();
      setOrderStatuses(data);
      console.log('Order Statuses:', data);
    } catch (error) {
      console.error('Error fetching order statuses:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">GraphQL Data Fetching Examples</h1>
      
      {loading && (
        <div className="mb-4 p-4 bg-blue-100 text-blue-700 rounded">
          Loading...
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Customer Data Section */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Customer Data</h2>
          <button
            onClick={handleFetchCustomer}
            disabled={loading}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            Fetch Customer Data
          </button>
          
          {customerData && (
            <div className="mt-4 p-3 bg-gray-100 rounded">
              <h3 className="font-medium">Customer Info:</h3>
              <p><strong>Name:</strong> {customerData.me?.name || 'N/A'}</p>
              <p><strong>Email:</strong> {customerData.me?.email || 'N/A'}</p>
              <p><strong>Active:</strong> {customerData.me?.is_active ? 'Yes' : 'No'}</p>
              <p><strong>Blocked:</strong> {customerData.me?.is_book_blocked ? 'Yes' : 'No'}</p>
            </div>
          )}
        </div>

        {/* Orders Data Section */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Orders Data</h2>
          <button
            onClick={handleFetchOrders}
            disabled={loading}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
          >
            Fetch Orders
          </button>
          
          {ordersData && (
            <div className="mt-4 p-3 bg-gray-100 rounded">
              <h3 className="font-medium">Orders Summary:</h3>
              <p><strong>Total Orders:</strong> {ordersData.orders?.paginatorInfo?.total || 0}</p>
              <p><strong>Current Page:</strong> {ordersData.orders?.paginatorInfo?.currentPage || 1}</p>
              <p><strong>Orders on Page:</strong> {ordersData.orders?.data?.length || 0}</p>
            </div>
          )}
        </div>

        {/* Single Order Section */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Single Order Data</h2>
          <div className="mb-3">
            <input
              type="text"
              placeholder="Enter Order ID"
              value={selectedOrderId}
              onChange={(e) => setSelectedOrderId(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>
          <button
            onClick={handleFetchOrder}
            disabled={loading || !selectedOrderId}
            className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
          >
            Fetch Order
          </button>
          
          {orderData && (
            <div className="mt-4 p-3 bg-gray-100 rounded">
              <h3 className="font-medium">Order Info:</h3>
              <p><strong>ID:</strong> {orderData.order?.display_id || orderData.order?.id || 'N/A'}</p>
              <p><strong>Status:</strong> {orderData.order?.status || 'N/A'}</p>
              <p><strong>Total:</strong> ${orderData.order?.total || 0}</p>
              <p><strong>Customer:</strong> {orderData.order?.customer_name || 'N/A'}</p>
            </div>
          )}
        </div>

        {/* Order Statuses Section */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Order Statuses</h2>
          <button
            onClick={handleFetchOrderStatuses}
            disabled={loading}
            className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 disabled:opacity-50"
          >
            Fetch Order Statuses
          </button>
          
          {orderStatuses && (
            <div className="mt-4 p-3 bg-gray-100 rounded">
              <h3 className="font-medium">Available Statuses:</h3>
              <div className="max-h-32 overflow-y-auto">
                {orderStatuses.orderStatuses?.map((status: any) => (
                  <div key={status.id} className="flex justify-between items-center py-1">
                    <span>{status.name}</span>
                    <span 
                      className="px-2 py-1 text-xs rounded"
                      style={{ backgroundColor: status.color || '#gray' }}
                    >
                      {status.slug}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Raw Data Display */}
      <div className="mt-8 bg-white p-4 rounded-lg shadow">
        <h2 className="text-lg font-semibold mb-3">Raw Data (Check Console)</h2>
        <p className="text-gray-600">
          Click any of the buttons above to fetch data. The raw responses will be logged to the browser console.
          You can also see formatted data in the sections above.
        </p>
      </div>
    </div>
  );
};

export default GraphQLDataExample;

import React from 'react';
import { useCustomer } from '@contexts/customer.context';

interface AuthProcessorProps {
  auth: any;
  children: React.ReactNode;
}

const AuthProcessor: React.FC<AuthProcessorProps> = ({ auth, children }) => {
  const { updateCustomer } = useCustomer();

  React.useEffect(() => {
    if (auth) {
      updateCustomer(auth);
    }
  }, [auth, updateCustomer]);

  return <>{children}</>;
};

export default AuthProcessor;

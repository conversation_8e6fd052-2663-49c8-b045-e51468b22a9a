import { useUI } from '@contexts/ui.context';
import { useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-toastify';
import http from '@framework/utils/http';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { setAuthToken, removeToken } from '@framework/utils/get-token';
import { renderErrors } from '@framework/utils/data-mappers';

import { STORAGE_KEY_ENUM } from '@utils/use-local-storage';
import { fetchCustomerData } from '@utils/graphql-queries';
import { useRouter } from 'next/router';
import { ROUTES } from '@utils/routes';

export interface LoginInputType {
  email: string;
  password: string;
}

async function login(input: any) {
  const { data } = await http.post(API_ENDPOINTS.LOGIN, input);
  return data;
}
export const useLoginMutation = () => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { authorize, unauthorize } = useUI();
  return useMutation(
    (input: LoginInputType) =>
      login({
        email: input.email,
        password: input.password,
      }),
    {
      onSuccess: async (data) => {
        if (data.data && data.data.token) {
          setAuthToken(data.data.token);
          authorize();

          // Invalidate cached data first
          queryClient.invalidateQueries(API_ENDPOINTS.ME);
          queryClient.invalidateQueries(API_ENDPOINTS.CART);
          queryClient.invalidateQueries(API_ENDPOINTS.REFER_CODE);

          // navigate to account page
          router.push(ROUTES.ACCOUNT);
        }
      },
      onError: (error: any) => {
        toast.error(renderErrors(error.response) ?? 'Login failed');
      },
    }
  );
};

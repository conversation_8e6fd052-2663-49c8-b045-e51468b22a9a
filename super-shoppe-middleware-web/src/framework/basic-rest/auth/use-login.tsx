import { useUI } from '@contexts/ui.context';
import { useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-toastify';
import http from '@framework/utils/http';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { setAuthToken, removeToken } from '@framework/utils/get-token';
import { renderErrors } from '@framework/utils/data-mappers';

import { STORAGE_KEY_ENUM } from '@utils/use-local-storage';
import { fetchMe } from 'src/data-graphql';

export interface LoginInputType {
  email: string;
  password: string;
}
async function login(input: any) {
  const { data } = await http.post(API_ENDPOINTS.LOGIN, input);
  return data;
}
export const useLoginMutation = () => {
  const queryClient = useQueryClient();
  const { authorize, unauthorize } = useUI();
  return useMutation(
    (input: LoginInputType) =>
      login({
        email: input.email,
        password: input.password,
      }),
    {
      onSuccess: async (data) => {
        if (data.data && data.data.token) {
          setAuthToken(data.data.token);
          authorize();

          // Invalidate cached data first
          queryClient.invalidateQueries(API_ENDPOINTS.ME);
          queryClient.invalidateQueries(API_ENDPOINTS.CART);
          queryClient.invalidateQueries(API_ENDPOINTS.REFER_CODE);

          // Force refetch customer data to validate conditions
          try {
            const customerData = await queryClient.fetchQuery(
              API_ENDPOINTS.ME,
              fetchMe
            );

            // Check customer conditions after login
            if (customerData?.me) {
              const customer = customerData.me;

              // Check if customer is active
              if (!customer.is_active) {
                // Clear token and unauthorized state
                removeToken(STORAGE_KEY_ENUM.AUTH_TOKEN);
                unauthorize();
                toast.error(
                  'Your account has been deactivated. Please contact support.'
                );
                return;
              }

              // Check if customer is blocked
              if (customer.is_book_blocked) {
                // Clear token and unauthorized state
                removeToken(STORAGE_KEY_ENUM.AUTH_TOKEN);
                unauthorize();
                toast.error('Your account has been temporarily blocked.');
                return;
              }

              // Check if email is verified (if required)
              if (!customer.email_verified_at && customer.email) {
                toast.warning(
                  'Please verify your email address to access all features.'
                );
              }

              toast.success('Login successful!');
            }
          } catch (error) {
            console.error('Error fetching customer data after login:', error);
            toast.error('Login successful, but failed to load user data.');
          }
        }
      },
      onError: (error: any) => {
        toast.error(renderErrors(error.response) ?? 'Login failed');
      },
    }
  );
};

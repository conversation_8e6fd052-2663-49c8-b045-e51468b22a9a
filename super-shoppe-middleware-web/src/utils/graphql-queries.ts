import graphql from '../framework/graphql/utils/graphql';

// GraphQL query for fetching customer data
export const GET_ME_QUERY = {
  query: `
    query Me {
      me {
        id
        is_guest
        ref_id
        customer_group_id
        commission_group_id
        firstname
        lastname
        name
        email
        email_verified_at
        phone
        country_code
        phone_verified_at
        gender
        dob
        dob_updated_at
        is_active
        is_book_blocked
        is_new
        shop_id
        created_at
        updated_at
        deleted_at
        profile {
          id
          avatar
          bio
          socials
          race
          street_address
          city
          zip
          state_id
        }
        permissions {
          id
          name
          guard_name
          created_at
          updated_at
        }
        roles {
          id
          name
          guard_name
          created_at
          updated_at
        }
      }
    }
  `,
};

// Function to fetch customer data using GraphQL
export const fetchCustomerData = async () => {
  try {
    const { data } = await graphql.post('', GET_ME_QUERY);
    return { me: data.data.me };
  } catch (error) {
    console.error('Error fetching customer data:', error);
    return { me: null };
  }
};

// GraphQL query for fetching orders
export const GET_ORDERS_QUERY = {
  query: `
    query GetOrders(
      $first: Int = 15
      $page: Int = 1
      $status: String
      $customer_id: ID
      $orderBy: [OrderByClause!]
    ) {
      orders(
        first: $first
        page: $page
        status: $status
        customer_id: $customer_id
        orderBy: $orderBy
      ) {
        paginatorInfo {
          count
          currentPage
          firstItem
          hasMorePages
          lastItem
          lastPage
          perPage
          total
        }
        data {
          id
          display_id
          tracking_number
          customer_name
          customer_contact
          customer_email
          status
          amount
          sales_tax
          paid_total
          total
          payment_gateway
          delivery_fee
          created_at
          updated_at
          customer {
            id
            name
            email
            phone
          }
          saleBy {
            id
            name
            email
          }
          orderProducts {
            id
            name
            sku
            barcode
            order_quantity
            unit_price
            subtotal
            product {
              id
              name
              image
            }
          }
          statusInfo {
            id
            name
            slug
            color
            serial
          }
        }
      }
    }
  `,
};

// Function to fetch orders using GraphQL
export const fetchOrdersData = async (variables = {}) => {
  try {
    const defaultVariables = {
      first: 15,
      page: 1,
      orderBy: [{ column: 'created_at', order: 'DESC' }],
      ...variables,
    };

    const { data } = await graphql.post('', {
      ...GET_ORDERS_QUERY,
      variables: defaultVariables,
    });

    return data.data;
  } catch (error) {
    console.error('Error fetching orders data:', error);
    return { orders: { data: [], paginatorInfo: null } };
  }
};

// GraphQL query for fetching single order
export const GET_ORDER_QUERY = {
  query: `
    query GetOrder($id: ID!) {
      order(id: $id) {
        id
        display_id
        external_order_id
        tracking_number
        type
        require_shipping
        customer_name
        customer_contact
        customer_email
        status
        amount
        sales_tax
        paid_total
        total
        total_invoiced
        total_refunded
        discount
        delivery_fee
        delivery_time
        payment_gateway
        payment_id
        shipping_address
        billing_address
        note
        created_at
        updated_at
        customer {
          id
          name
          firstname
          lastname
          email
          phone
          is_active
        }
        saleBy {
          id
          name
          email
        }
        orderProducts {
          id
          name
          sku
          barcode
          order_quantity
          invoiced_quantity
          shipped_quantity
          canceled_quantity
          refunded_quantity
          unit_price
          discount
          subtotal
          tax
          product {
            id
            name
            slug
            description
            image
            gallery
          }
        }
        statusInfo {
          id
          name
          slug
          color
          serial
        }
        parent {
          id
          display_id
          status
        }
        children {
          id
          display_id
          status
          total
        }
      }
    }
  `,
};

// Function to fetch single order using GraphQL
export const fetchOrderData = async (orderId: string) => {
  try {
    const { data } = await graphql.post('', {
      ...GET_ORDER_QUERY,
      variables: { id: orderId },
    });

    return data.data;
  } catch (error) {
    console.error('Error fetching order data:', error);
    return { order: null };
  }
};

// GraphQL query for fetching order statuses
export const GET_ORDER_STATUSES_QUERY = {
  query: `
    query GetOrderStatuses {
      orderStatuses {
        id
        name
        slug
        serial
        color
        default
      }
    }
  `,
};

// Function to fetch order statuses using GraphQL
export const fetchOrderStatusesData = async () => {
  try {
    const { data } = await graphql.post('', GET_ORDER_STATUSES_QUERY);
    return data.data;
  } catch (error) {
    console.error('Error fetching order statuses:', error);
    return { orderStatuses: [] };
  }
};
